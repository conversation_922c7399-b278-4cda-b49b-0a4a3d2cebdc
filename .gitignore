/mysoft-demo.iml
/.idea/
HELP.md
target/
!.mvn/wrapper/maven-wrapper.jar
*.jar
*.iml
.idea/
.target/
/target/
.settings/
.classpath
.project
/ci/config/mysql/data/
/ci/packages/platform/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
*.log

### NetBeans ###
/nbproject/private/
/nbbuild/
/mysoft-container/dist/
/nbdist/
/.nb-gradle/
build/

### VS Code ###
.vscode/
/basic-service.iml
/biz-common.iml

# 元数据签出设计时文件
*.metadata.design.config
# 元数据临时文件

_metadata/EntityLog
_metadata/PublishLog.metadata.config
_metadata/MetadataStatus.metadata.config

### tempfile ###
/ci/packages
/ci/packages/.gitignore
/temp/

# 日志
ci/tools/DbSync/log/
ci/tools/PackageClient/logs/

ci/packages/

# 前端打包输出文件
/data/statis/gptbuilder/gpt
/data/statis/gptbuilder/designer
/data/statis/gptbuilder/assistant

data/metadata/_metadata/MetadataStatus.metadata.config
/src/.flattened-pom.xml
/src/gptbuilder-agent/.flattened-pom.xml
/src/gptbuilder-agent/gptbuilder-agent-interfaces/.flattened-pom.xml
/src/gptbuilder-agent/gptbuilder-agent-model/.flattened-pom.xml
/src/gptbuilder-agent/gptbuilder-agent-rmi/.flattened-pom.xml
/src/gptbuilder-agent/gptbuilder-agent-service/.flattened-pom.xml
/src/gptbuilder-common/.flattened-pom.xml
/src/gptbuilder-common/gptbuilder-common-interfaces/.flattened-pom.xml
/src/gptbuilder-common/gptbuilder-common-model/.flattened-pom.xml
/src/gptbuilder-common/gptbuilder-common-service/.flattened-pom.xml
/src/gptbuilder-common/gptbuilder-common-util/.flattened-pom.xml
/src/gptbuilder-container/.flattened-pom.xml
/src/gptbuilder-demo/.flattened-pom.xml
/src/gptbuilder-demo/gptbuilder-demo-interfaces/.flattened-pom.xml
/src/gptbuilder-demo/gptbuilder-demo-model/.flattened-pom.xml
/src/gptbuilder-demo/gptbuilder-demo-service/.flattened-pom.xml
/src/gptbuilder-plugin/.flattened-pom.xml
/src/gptbuilder-plugin/gptbuilder-plugin-interfaces/.flattened-pom.xml
/src/gptbuilder-plugin/gptbuilder-plugin-model/.flattened-pom.xml
/src/gptbuilder-plugin/gptbuilder-plugin-service/.flattened-pom.xml
/src/gptbuilder-starter/.flattened-pom.xml
.vs/slnx.sqlite
data/statis/gptbuilder/web.config
/src/gptbuilder-agent/gptbuilder-agent-import/.flattened-pom.xml
/src/gptbuilder-agent/gptbuilder-agent-entity/.flattened-pom.xml
/src/gptbuilder-container/src/main/resources/bootstrap-dev.yml.bak

.run
/logs
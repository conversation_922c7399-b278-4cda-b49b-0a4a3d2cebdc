<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mysoft.gptbuilder</groupId>
    <artifactId>gptbuilder</artifactId>
    <version>${revision}</version>
  </parent>
  <artifactId>gptbuilder-container</artifactId>
  <properties>
    <maven-exec-plugin.version>3.0.0</maven-exec-plugin.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.mysoft.gptbuilder</groupId>
      <artifactId>gptbuilder-starter</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.mysoft.framework.container</groupId>
      <artifactId>mysoft-container</artifactId>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <version>3.5.0</version>
        <configuration>
          <executable>java</executable>
          <mainClass>com.mysoft.framework.container.PlatformApplication</mainClass>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <version>3.2.0</version>
            <executions>
              <execution>
                <id>make-assembly</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <finalName>${packageName}</finalName>
              <appendAssemblyId>false</appendAssemblyId>
              <descriptors>
                <descriptor>src/main/resources/assembly/assembly.xml</descriptor>
              </descriptors>
              <outputDirectory>${dir}/dist</outputDirectory>
              <encoding>UTF-8</encoding>
              <skipAssembly>${skipAssembly}</skipAssembly>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>exec</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <configuration>
              <classifier>exec</classifier>
              <mainClass>com.mysoft.framework.container.PlatformApplication</mainClass>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>

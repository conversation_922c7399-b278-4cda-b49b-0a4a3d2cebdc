server:
  port: 8082
spring:
  application:
    name: gptbuilder
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
  web:
    resources:
      static-locations:
      - file:D:\workspace\gptbuilder\data\statis
mysoft:
  metadata:
    mode: site
  application:
    code: '4200'
    basePackages: com.mysoft.gptbuilder
  event:
    previously:
      enabled: true
  backgroundjob:
    jobSwitch: true
  rabbit:
    concurrentConsumers: 10
  sdk:
    log:
      enableLogManager: false
    nacos:
      config:
        enable: true
mybatis-plus:
  configuration:
    cache-enabled: false
    local-cache-scope: statement
SKYLINE_NACOS_SERVER_ADDRESSES: http://localhost:8848
SKYLINE_NACOS_DATA_ID: java_product.properties
SKYLINE_NACOS_NAME: java_product
SKYLINE_NACOS_USERNAME: nacos
SKYLINE_NACOS_PASSWORD: nacos
SKYLINE_NACOS_NAMESPACE_ID: skyline
SKYLINE_NACOS_GROUP: jmpt-j-multi

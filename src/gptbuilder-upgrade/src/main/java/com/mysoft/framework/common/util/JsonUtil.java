package com.mysoft.framework.common.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.TimeZone;
import java.util.UUID;

public class JsonUtil {
    private static JsonMapper jsonMapper = new JsonMapper();

    static {

        jsonMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        jsonMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 忽略无法转换的对象
        jsonMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        //使用标准的 Bean 命名策略，区别与 Jackson 历史的命名策略，默认会使用历史策略

        SimpleModule entityModule = new SimpleModule();
        entityModule.addDeserializer(UUID.class, new UuidDeserializer());
        jsonMapper.registerModule(entityModule);

        //日期时间设置
        // 指定时区
        jsonMapper.setTimeZone(TimeZone.getDefault());
    }
    public static <T> T parse(String json, Class<T> clazz) {
        if (json != null) {
            try {
                return jsonMapper.readValue(json, clazz);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static <T> T parse(String json, TypeReference<T> typeReference) {
        if (json != null) {
            try {
                return jsonMapper.readValue(json, typeReference);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static String toJson(Object object) {
        if (object != null) {
            try {
                return jsonMapper.writeValueAsString(object);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static String toString(Object object) {
        return toJson(object);
    }

    public static JsonNode parseTree(String json) {
        if (json != null) {
            try {
                return jsonMapper.readTree(json);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }
}

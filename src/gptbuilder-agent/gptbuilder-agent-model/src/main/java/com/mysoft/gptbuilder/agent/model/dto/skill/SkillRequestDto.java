package com.mysoft.gptbuilder.agent.model.dto.skill;

import com.google.gson.annotations.SerializedName;
import com.mysoft.framework.common.util.JsonUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class SkillRequestDto {

    /**
     * 技能GUID
     */
    @SerializedName("id")
    private String id;

    /**
     * 技能编码
     */
    @SerializedName("code")
    private String code;

    /**
     * 技能名称
     */
    @SerializedName("name")
    private String name;

    /**
     * 对话窗口
     */
    @SerializedName("openDialogWindow")
    private int openDialogWindow;

    /**
     * 图标
     */
    @SerializedName("icon")
    private String icon;

    /**
     * 技能状态
     */
    @SerializedName("status")
    private String status;


    /**
     * 模型实例编码
     */
    @SerializedName("modelInstanceCode")
    private String modelInstanceCode;

    /**
     * 技能说明
     */
    @SerializedName("description")
    private String description;

    /**
     * 模式：prompt:提示模式；flow:编排模式
     */
    @SerializedName("mode")
    private String mode;

    @SerializedName("questions")
    private List<SkillQuestionsDto> questions;

    @SerializedName("guide")
    private String guide;

    @SerializedName("welcome")
    private String welcome;

    @SerializedName("spaceGUID")
    private String spaceGUID;

    @SerializedName("startup")
    SkillStartupDto startup;

    @SerializedName("metaDataVersion")
    String metaDataVersion;

    @SerializedName("examples")
    private List<SkillUserExampleDto> examples;

    /**
     * 编排数据
     */
    @SerializedName("flow")
    private FlowDTO flow;

    /**
     * 编排数据
     */
    @SerializedName("agent")
    private AgentDTO agent;

    /**
     * 保存模式；1:基础保存（头表保存）；2:临时保存；3：发布保存
     */
    @SerializedName("saveMode")
    private Integer saveMode;

    @SerializedName("initEvent")
    private String initEvent;

    @SerializedName("skillCategoryGUIDs")
    private String skillCategoryGUIDs;

    List<OrchestrationMetadata> orchestrations;

    public static SkillRequestDto coverToDto(String metadata) {
        //判断字符串是否为 json 的工具
        return JsonUtil.parse(metadata, SkillRequestDto.class);
    }

    /**
     * 编排数据Item
     */
    @NoArgsConstructor
    @Data
    public static class FlowDTO {
        /**
         * 节点数组
         */
        @SerializedName("nodes")
        private List<SkillNodeDto> nodes;
        /**
         * 连接线数据
         */
        @SerializedName("edges")
        private List<EdgesDTO> edges;

        /**
         * 连接线数据Item
         */
        @NoArgsConstructor
        @Data
        public static class EdgesDTO {
            /**
             * 连接线编码
             */
            @SerializedName("code")
            private String code;
            /**
             * 来源节点
             */
            @SerializedName("source")
            private String source;
            /**
             * 目标节点
             */
            @SerializedName("target")
            private String target;
        }
    }

    @NoArgsConstructor
    @Data
    public static class AgentDTO {

        AgentPromptDto prompt;

        List<AgentPluginDto> tools;

        List<AgentMcpDto> mcps;

        List<AgentKnowledgeDto> knowledgs;

        @SerializedName("inputs")
        private List<AbstractNodeConfigDto.ParamDto> inputs;

        /**
         * 最大轮次
         */
        private int maxResultNumber;

        /**
         * 文件类型
         */
        private String uploadables;

        /**
         * 是否使用文件上传
         */
        private boolean useFileUpload = false;

        /**
         * 是否使用扫码上传
         */
        private boolean useQRCodeUpload = false;
        /**
         * 模型实例编码
         */
        private String  modelInstanceCode = "";
        /**
         * 模型多样性
         */
        private String  generationDiversity = "";
        /**
         * 模型设置
         */
        private executionSettingDto executionSetting;

        /**
         * 允许上传文件数
         */
        private int maxUploadNumber;

        /**
         * 携带上下文轮数
         */
        private int maxContextTurnsNumber;
    }

    @NoArgsConstructor
    @Data
    public static class OrchestrationMetadata {

        private String Id;

        private String OrchestrationTemplate;

        private List<SkillOrchestrationContextDto.OrchestrationPrompt> prompts;

        private List<SkillOrchestrationContextDto.OrchestrationPlugin> plugins;
    }

    @NoArgsConstructor
    @Data
    public static class SkillStartupDto {

        private String pluginGUID;

        private String toolGUID;

    }

    @NoArgsConstructor
    @Data
    public static class AgentPluginDto {
        public String pluginGUID;

        public String toolGUID;

        public String toolCode;

        public String name;

        public String path;

        // 入参
        String inputs;

        // 返回参数
        String outputs;

        public String description;
    }

    @NoArgsConstructor
    @Data
    public static class AgentMcpDto {
        public String serviceGUID;

        public String serviceCode;

        public String toolGUID;

        public String toolName;

        /**
         * 服务名称
         */
        public String serviceName;

        /**
         * 服务描述
         */
        public String serviceDescription;

        /**
         * 工具描述
         */
        public String toolDescription;

        public String serviceIcon;

    }

    @NoArgsConstructor
    @Data
    public static class AgentKnowledgeDto {
        public String id;

        public String code;

        public String name;

        public String description;

        public Integer type;
    }

    @NoArgsConstructor
    @Data
    public static class AgentPromptDto {
        public String template;

        private List<AbstractNodeConfigDto.ParamDto> inputs;
    }

    @NoArgsConstructor
    @Data
    public static class executionSettingDto {
        public Float temperature;

        public Float top_p;

        public Integer max_tokens;

        public Boolean enable_thinking;

        public Integer thinking_budget;
    }
}

package com.mysoft.gptbuilder.agent.model.dto.skill;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/**
 * 自定义反序列化器，用于处理executionSetting字段的空字符串问题
 */
public class ExecutionSettingDeserializer extends JsonDeserializer<SkillRequestDto.executionSettingDto> {

    @Override
    public SkillRequestDto.executionSettingDto deserialize(JsonParser p, DeserializationContext ctxt) 
            throws IOException, JsonProcessingException {
        
        String text = p.getText();
        
        // 如果是空字符串或null，返回null
        if (StringUtils.isEmpty(text) || "null".equals(text)) {
            return null;
        }
        
        // 如果是JSON对象字符串，正常反序列化
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode node = mapper.readTree(text);
            return mapper.treeToValue(node, SkillRequestDto.executionSettingDto.class);
        } catch (Exception e) {
            // 如果解析失败，返回null
            return null;
        }
    }
}

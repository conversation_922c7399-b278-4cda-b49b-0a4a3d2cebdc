import com.mysoft.framework.common.util.JsonUtil;
import com.mysoft.gptbuilder.agent.model.dto.skill.SkillRequestDto;

/**
 * 测试 maxContextTurnsNumber 字段的序列化和反序列化
 */
public class TestMaxContextTurnsNumberFix {
    
    public static void main(String[] args) {
        // 模拟前端发送的数据
        String frontendData = """
        {
            "id":"c3ae4027-2b09-430a-bc43-6d3d34787120",
            "code":"tj_dap_my_skill_final",
            "name":"秋秋测试",
            "openDialogWindow":1,
            "icon":"",
            "status":"1",
            "modelInstanceCode":"",
            "description":"",
            "mode":"agent",
            "questions":[],
            "guide":null,
            "welcome":null,
            "spaceGUID":"3a1b4825-7afb-50a3-0ee1-30ee1051d18a",
            "startup":null,
            "metaDataVersion":"SV3.0",
            "examples":[],
            "flow":null,
            "agent":{
                "prompt":{"template":"","inputs":[]},
                "tools":[],
                "mcps":[],
                "knowledgs":[{"id":"7cc53453-b3a6-497d-a6c6-e3984ba9b15e","code":"tj_dap_kn_tj_dap_chatbi_3a1b6d2cea4c813a82d06e8c729ae939","name":"销售业绩2","description":"销售业绩","type":3}],
                "inputs":[],
                "maxResultNumber":10,
                "uploadables":"",
                "useFileUpload":false,
                "useQRCodeUpload":false,
                "modelInstanceCode":"default_text_generation",
                "generationDiversity":"precise",
                "executionSetting":{"temperature":0.1,"top_p":0.8,"max_tokens":8192,"enable_thinking":null,"thinking_budget":null},
                "maxUploadNumber":0,
                "maxContextTurnsNumber":13
            },
            "saveMode":1,
            "initEvent":"",
            "skillCategoryGUIDs":null,
            "orchestrations":null
        }
        """;
        
        try {
            // 测试反序列化
            SkillRequestDto skillRequestDto = JsonUtil.parse(frontendData, SkillRequestDto.class);
            
            System.out.println("=== 反序列化测试 ===");
            System.out.println("技能ID: " + skillRequestDto.getId());
            System.out.println("技能名称: " + skillRequestDto.getName());
            
            if (skillRequestDto.getAgent() != null) {
                System.out.println("Agent配置存在");
                System.out.println("maxResultNumber: " + skillRequestDto.getAgent().getMaxResultNumber());
                System.out.println("maxUploadNumber: " + skillRequestDto.getAgent().getMaxUploadNumber());
                System.out.println("maxContextTurnsNumber: " + skillRequestDto.getAgent().getMaxContextTurnsNumber());
            } else {
                System.out.println("Agent配置为空");
            }
            
            // 测试序列化
            String serializedData = JsonUtil.toString(skillRequestDto);
            System.out.println("\n=== 序列化测试 ===");
            System.out.println("序列化后的数据包含maxContextTurnsNumber: " + 
                serializedData.contains("maxContextTurnsNumber"));
            
            // 再次反序列化验证
            SkillRequestDto deserializedAgain = JsonUtil.parse(serializedData, SkillRequestDto.class);
            System.out.println("\n=== 二次反序列化测试 ===");
            if (deserializedAgain.getAgent() != null) {
                System.out.println("maxContextTurnsNumber保持一致: " + 
                    (deserializedAgain.getAgent().getMaxContextTurnsNumber() == 13));
            }
            
            System.out.println("\n测试完成！");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
